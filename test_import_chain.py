#!/usr/bin/env python3
"""
Test the import chain to identify any remaining issues.
"""

import sys
import os

def test_import_chain():
    """Test the import chain step by step."""
    print("Testing import chain...")
    
    try:
        # Add the report generation module to path
        sys.path.insert(0, os.path.join(os.getcwd(), 'src', 'report_generation'))
        
        print("1. Testing setup_fonts import...")
        import setup_fonts
        print("   ✓ setup_fonts imported successfully")
        
        print("2. Testing layout_config import...")
        import layout_config
        print("   ✓ layout_config imported successfully")
        
        print("3. Testing data_collector import...")
        try:
            import data_collector
            print("   ✓ data_collector imported successfully")
        except Exception as e:
            print(f"   ⚠ data_collector import failed: {e}")
        
        print("4. Testing image_handler import...")
        try:
            import image_handler
            print("   ✓ image_handler imported successfully")
        except Exception as e:
            print(f"   ⚠ image_handler import failed: {e}")
        
        print("5. Testing base_pdf import (without fpdf)...")
        try:
            # Mock fpdf to test the import structure
            sys.modules['fpdf'] = type(sys)('fpdf')
            sys.modules['fpdf'].FPDF = object
            import base_pdf
            print("   ✓ base_pdf import structure is correct")
        except Exception as e:
            print(f"   ✗ base_pdf import failed: {e}")
            return False
        
        print("6. Testing report_generator import (without dependencies)...")
        try:
            # Mock PIL to test the import structure
            sys.modules['PIL'] = type(sys)('PIL')
            sys.modules['PIL'].Image = object
            import report_generator
            print("   ✓ report_generator import structure is correct")
        except Exception as e:
            print(f"   ✗ report_generator import failed: {e}")
            return False
        
        print("\n✅ All import tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Import chain test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_import_chain()
    if success:
        print("\n🎉 Import chain is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ Import chain has issues!")
        sys.exit(1)
