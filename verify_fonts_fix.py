#!/usr/bin/env python3
"""
Verification script to test the simplified fonts setup.
This script tests the core functionality without requiring external dependencies.
"""

import os
import sys

def test_fonts_setup():
    """Test the fonts setup functionality."""
    print("=" * 60)
    print("VAPR-iDEX Fonts Fix Verification")
    print("=" * 60)
    
    try:
        # Add the report generation module to path
        sys.path.insert(0, os.path.join(os.getcwd(), 'src', 'report_generation'))
        import setup_fonts
        
        print("✓ Successfully imported setup_fonts module")
        
        # Test get_fonts_dir function
        fonts_dir = setup_fonts.get_fonts_dir()
        print(f"✓ Fonts directory: {fonts_dir}")
        
        # Verify the directory exists
        if os.path.exists(fonts_dir):
            print("✓ Fonts directory exists")
        else:
            print(f"✗ Fonts directory does not exist: {fonts_dir}")
            return False
        
        # Test setup_fonts function
        setup_result = setup_fonts.setup_fonts()
        if setup_result:
            print("✓ setup_fonts() returned True")
        else:
            print("✗ setup_fonts() returned False")
            return False
        
        # Verify required fonts exist
        required_fonts = ['DejaVuSansCondensed.ttf', 'DejaVuSansCondensed-Bold.ttf']
        for font in required_fonts:
            font_path = os.path.join(fonts_dir, font)
            if os.path.exists(font_path):
                print(f"✓ Found: {font}")
            else:
                print(f"✗ Missing: {font}")
                return False
        
        # Test that the fonts directory is at the same level as gui_main.py
        gui_main_path = os.path.join(os.getcwd(), 'gui_main.py')
        if os.path.exists(gui_main_path):
            print("✓ gui_main.py found at project root")
            expected_fonts_dir = os.path.join(os.getcwd(), 'fonts')
            if fonts_dir == expected_fonts_dir:
                print("✓ Fonts directory is at the same level as gui_main.py")
            else:
                print(f"✗ Fonts directory mismatch. Expected: {expected_fonts_dir}, Got: {fonts_dir}")
                return False
        else:
            print("✗ gui_main.py not found at project root")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 ALL TESTS PASSED!")
        print("The fonts fix has been successfully implemented.")
        print("Key improvements:")
        print("  • Simple and straightforward font path detection")
        print("  • Fonts folder at same level as gui_main.py")
        print("  • Centralized font directory management")
        print("  • Works for both development and PyInstaller")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"✗ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_import_structure():
    """Test that the import structure works correctly."""
    print("\nTesting import structure...")
    
    try:
        # Test that we can import the functions without circular imports
        sys.path.insert(0, os.path.join(os.getcwd(), 'src', 'report_generation'))
        
        # Test individual imports
        import setup_fonts
        print("✓ setup_fonts module imported successfully")
        
        # Test function access
        get_fonts_dir = setup_fonts.get_fonts_dir
        setup_fonts_func = setup_fonts.setup_fonts
        print("✓ Functions accessible from module")
        
        # Test that global variable works
        fonts_dir1 = get_fonts_dir()
        fonts_dir2 = get_fonts_dir()
        if fonts_dir1 == fonts_dir2:
            print("✓ Global variable caching works correctly")
        else:
            print("✗ Global variable caching failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Import structure test failed: {str(e)}")
        return False

if __name__ == "__main__":
    # Run the tests
    fonts_test = test_fonts_setup()
    import_test = test_import_structure()
    
    if fonts_test and import_test:
        print("\n🎉 All verification tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some verification tests failed!")
        sys.exit(1)
