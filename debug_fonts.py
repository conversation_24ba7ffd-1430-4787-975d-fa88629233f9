#!/usr/bin/env python3
"""
Debug script to check font paths during report generation.
"""

import os
import sys

def debug_font_paths():
    """Debug font path detection."""
    print("="*60)
    print("FONT PATH DEBUG")
    print("="*60)
    
    print(f"Current working directory: {os.getcwd()}")
    print(f"Script location: {__file__}")
    print(f"Python executable: {sys.executable}")
    
    # Test direct import
    try:
        sys.path.insert(0, os.path.join(os.getcwd(), 'src', 'report_generation'))
        import setup_fonts
        
        fonts_dir = setup_fonts.get_fonts_dir()
        print(f"Detected fonts directory: {fonts_dir}")
        print(f"Fonts directory exists: {os.path.exists(fonts_dir)}")
        
        if os.path.exists(fonts_dir):
            files = os.listdir(fonts_dir)
            print(f"Files in fonts directory: {files}")
            
            required_fonts = ['DejaVuSansCondensed.ttf', 'DejaVuSansCondensed-Bold.ttf']
            for font in required_fonts:
                font_path = os.path.join(fonts_dir, font)
                exists = os.path.exists(font_path)
                print(f"  {font}: {'✓' if exists else '✗'} ({font_path})")
        
        # Test setup_fonts function
        setup_result = setup_fonts.setup_fonts()
        print(f"setup_fonts() result: {setup_result}")
        
    except Exception as e:
        print(f"Error testing setup_fonts: {e}")
        import traceback
        traceback.print_exc()
    
    # Test FPDF font path
    try:
        print("\n" + "="*60)
        print("FPDF FONT PATH TEST")
        print("="*60)
        
        from fpdf import FPDF
        print(f"FPDF.FONT_PATH before: {getattr(FPDF, 'FONT_PATH', 'Not set')}")
        
        # Import the report generation module to see if it sets the path
        try:
            from src.report_generation.setup_fonts import get_fonts_dir
            fonts_dir = get_fonts_dir()
            FPDF.FONT_PATH = fonts_dir
            print(f"FPDF.FONT_PATH after setting: {FPDF.FONT_PATH}")
        except Exception as e:
            print(f"Error setting FPDF.FONT_PATH: {e}")
        
    except Exception as e:
        print(f"Error testing FPDF: {e}")
    
    # Test creating a simple PDF
    try:
        print("\n" + "="*60)
        print("PDF CREATION TEST")
        print("="*60)
        
        from fpdf import FPDF
        from src.report_generation.setup_fonts import get_fonts_dir
        
        fonts_dir = get_fonts_dir()
        pdf = FPDF()
        pdf.add_page()
        
        # Try to add fonts
        regular_font = os.path.join(fonts_dir, 'DejaVuSansCondensed.ttf')
        bold_font = os.path.join(fonts_dir, 'DejaVuSansCondensed-Bold.ttf')
        
        print(f"Regular font path: {regular_font}")
        print(f"Regular font exists: {os.path.exists(regular_font)}")
        print(f"Bold font path: {bold_font}")
        print(f"Bold font exists: {os.path.exists(bold_font)}")
        
        if os.path.exists(regular_font) and os.path.exists(bold_font):
            pdf.add_font('DejaVu', '', regular_font, uni=True)
            pdf.add_font('DejaVu', 'B', bold_font, uni=True)
            pdf.set_font('DejaVu', '', 12)
            pdf.cell(0, 10, 'Test PDF creation', 0, 1, 'C')
            
            test_output = 'debug_test.pdf'
            pdf.output(test_output)
            
            if os.path.exists(test_output):
                print(f"✓ PDF created successfully: {test_output}")
                os.remove(test_output)  # Clean up
            else:
                print("✗ PDF was not created")
        else:
            print("✗ Font files not found, cannot test PDF creation")
            
    except Exception as e:
        print(f"Error testing PDF creation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_font_paths()
