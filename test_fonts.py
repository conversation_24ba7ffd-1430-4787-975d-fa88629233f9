#!/usr/bin/env python3
"""
Test script to verify that the simplified fonts setup works correctly.
"""

import os
import sys

def test_fonts_setup():
    """Test the fonts setup functionality."""
    print("Testing fonts setup...")

    try:
        # Import the setup functions directly
        sys.path.insert(0, os.path.join(os.getcwd(), 'src', 'report_generation'))
        import setup_fonts
        get_fonts_dir = setup_fonts.get_fonts_dir
        setup_fonts_func = setup_fonts.setup_fonts
        
        # Test get_fonts_dir function
        fonts_dir = get_fonts_dir()
        print(f"Fonts directory: {fonts_dir}")

        # Verify the directory exists
        if not os.path.exists(fonts_dir):
            print(f"ERROR: Fonts directory does not exist: {fonts_dir}")
            return False

        # Test setup_fonts function
        setup_result = setup_fonts_func()
        print(f"Setup fonts result: {setup_result}")
        
        if not setup_result:
            print("ERROR: setup_fonts() returned False")
            return False
        
        # Verify required fonts exist
        required_fonts = ['DejaVuSansCondensed.ttf', 'DejaVuSansCondensed-Bold.ttf']
        for font in required_fonts:
            font_path = os.path.join(fonts_dir, font)
            if os.path.exists(font_path):
                print(f"✓ Found: {font}")
            else:
                print(f"✗ Missing: {font}")
                return False
        
        print("✓ All tests passed!")
        return True
        
    except Exception as e:
        print(f"ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_creation():
    """Test basic PDF creation with fonts."""
    print("\nTesting PDF creation...")

    try:
        # Import FPDF directly for a simpler test
        from fpdf import FPDF
        import setup_fonts

        # Get fonts directory
        fonts_dir = setup_fonts.get_fonts_dir()
        
        # Create a simple PDF
        pdf = FPDF()
        pdf.add_page()

        # Add fonts
        regular_font = os.path.join(fonts_dir, 'DejaVuSansCondensed.ttf')
        bold_font = os.path.join(fonts_dir, 'DejaVuSansCondensed-Bold.ttf')

        pdf.add_font('DejaVu', '', regular_font, uni=True)
        pdf.add_font('DejaVu', 'B', bold_font, uni=True)

        pdf.set_font('DejaVu', '', 12)
        pdf.cell(0, 10, 'Test PDF with DejaVu font', 0, 1, 'C')
        pdf.set_font('DejaVu', 'B', 14)
        pdf.cell(0, 10, 'Bold text test', 0, 1, 'C')
        
        # Save to test file
        test_pdf_path = 'test_fonts_output.pdf'
        pdf.output(test_pdf_path)
        
        if os.path.exists(test_pdf_path):
            print(f"✓ PDF created successfully: {test_pdf_path}")
            # Clean up
            os.remove(test_pdf_path)
            return True
        else:
            print("✗ PDF was not created")
            return False
            
    except Exception as e:
        print(f"ERROR creating PDF: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("VAPR-iDEX Fonts Test")
    print("=" * 50)
    
    # Test fonts setup
    fonts_ok = test_fonts_setup()
    
    # Test PDF creation if fonts are OK
    if fonts_ok:
        pdf_ok = test_pdf_creation()
        
        if fonts_ok and pdf_ok:
            print("\n🎉 All tests passed! Fonts are working correctly.")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed.")
            sys.exit(1)
    else:
        print("\n❌ Fonts setup failed.")
        sys.exit(1)
